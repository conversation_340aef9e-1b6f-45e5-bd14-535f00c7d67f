# Python
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
env/
venv/
pip-wheel-metadata/
.eggs/
*.egg-info/
dist/
build/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# Frontend build output
web/static/dist/
web/frontend/dist/
web/frontend/build/

# Frontend development
web/frontend/.env.local
web/frontend/.env.development.local
web/frontend/.env.test.local
web/frontend/.env.production.local

# Worlds
worlds/*.json

# IDE / OS
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store

logs/
