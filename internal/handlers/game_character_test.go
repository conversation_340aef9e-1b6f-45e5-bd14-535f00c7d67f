package handlers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"ai-text-game-iam-npc/internal/auth"
	"ai-text-game-iam-npc/internal/game"
	"ai-text-game-iam-npc/internal/models"
	"ai-text-game-iam-npc/internal/testutil"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// setupGameHandler 设置游戏处理器测试环境
func setupGameHandler(t *testing.T) (*GameHandler, *gin.Engine, *testutil.TestDB) {
	gin.SetMode(gin.TestMode)
	
	testDB := testutil.SetupTestDB(t)
	logger := testutil.CreateTestLogger()
	
	worldService := game.NewWorldService(testDB.DB, logger)
	characterService := game.NewCharacterService(testDB.DB, logger)
	sceneService := game.NewSceneService(testDB.DB, logger)
	eventService := game.NewEventService(testDB.DB, logger)
	stateService := game.NewStateService(testDB.DB, logger)
	
	handler := NewGameHandler(worldService, characterService, sceneService, eventService, stateService)
	
	router := gin.New()
	router.Use(func(c *gin.Context) {
		// 模拟认证中间件，设置测试用户ID
		testUserID := uuid.New()
		c.Set("user_id", testUserID)
		c.Next()
	})
	
	return handler, router, testDB
}

func TestGameHandler_GetWorldCharacters(t *testing.T) {
	testutil.SkipIfNoDatabase(t)

	handler, router, testDB := setupGameHandler(t)
	defer testDB.CleanupTestDB(t)

	// 创建测试数据
	user := testDB.CreateTestUser(t)
	world := testDB.CreateTestWorld(t, user.ID)
	
	// 创建测试角色
	playerCharacter := testDB.CreateTestCharacter(t, world.ID, user.ID)
	playerCharacter.CharacterType = "player"
	testDB.DB.Save(playerCharacter)
	
	npcCharacter := &models.Character{
		WorldID:       world.ID,
		UserID:        nil, // NPC没有用户ID
		Name:          "测试NPC",
		Description:   testutil.StringPtr("一个测试NPC角色"),
		CharacterType: "npc",
		Traits:        models.StringArray{"智慧", "友善"},
		Status:        "active",
	}
	err := testDB.DB.Create(npcCharacter).Error
	require.NoError(t, err, "创建NPC角色失败")

	// 设置路由
	router.GET("/game/worlds/:world_id/characters", handler.GetWorldCharacters)

	tests := []struct {
		name           string
		worldID        string
		queryParams    string
		expectedStatus int
		expectedCount  int
		characterType  string
		description    string
	}{
		{
			name:           "获取所有角色",
			worldID:        world.ID.String(),
			queryParams:    "?page=1&limit=10",
			expectedStatus: http.StatusOK,
			expectedCount:  2, // 1个玩家角色 + 1个NPC角色
			characterType:  "",
			description:    "应该返回世界中的所有角色",
		},
		{
			name:           "只获取玩家角色",
			worldID:        world.ID.String(),
			queryParams:    "?page=1&limit=10&character_type=player",
			expectedStatus: http.StatusOK,
			expectedCount:  1,
			characterType:  "player",
			description:    "应该只返回玩家角色",
		},
		{
			name:           "只获取NPC角色",
			worldID:        world.ID.String(),
			queryParams:    "?page=1&limit=10&character_type=npc",
			expectedStatus: http.StatusOK,
			expectedCount:  1,
			characterType:  "npc",
			description:    "应该只返回NPC角色",
		},
		{
			name:           "分页测试",
			worldID:        world.ID.String(),
			queryParams:    "?page=1&limit=1",
			expectedStatus: http.StatusOK,
			expectedCount:  1,
			characterType:  "",
			description:    "分页应该正确限制返回数量",
		},
		{
			name:           "无效世界ID",
			worldID:        "invalid-uuid",
			queryParams:    "?page=1&limit=10",
			expectedStatus: http.StatusBadRequest,
			expectedCount:  0,
			characterType:  "",
			description:    "无效的世界ID应该返回400错误",
		},
		{
			name:           "不存在的世界",
			worldID:        uuid.New().String(),
			queryParams:    "?page=1&limit=10",
			expectedStatus: http.StatusNotFound,
			expectedCount:  0,
			characterType:  "",
			description:    "不存在的世界应该返回404错误",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 构建请求URL
			url := fmt.Sprintf("/game/worlds/%s/characters%s", tt.worldID, tt.queryParams)
			
			// 创建HTTP请求
			req, err := http.NewRequest("GET", url, nil)
			require.NoError(t, err, "创建HTTP请求失败")

			// 执行请求
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			// 验证响应状态码
			assert.Equal(t, tt.expectedStatus, w.Code, tt.description)

			// 解析响应体
			var response Response
			err = json.Unmarshal(w.Body.Bytes(), &response)
			require.NoError(t, err, "解析响应体失败")

			if tt.expectedStatus == http.StatusOK {
				// 验证成功响应
				assert.True(t, response.Success, "响应应该标记为成功")
				assert.NotNil(t, response.Data, "响应数据不应该为空")

				// 解析分页响应数据
				dataBytes, err := json.Marshal(response.Data)
				require.NoError(t, err, "序列化响应数据失败")

				var paginatedResponse PaginatedResponse
				err = json.Unmarshal(dataBytes, &paginatedResponse)
				require.NoError(t, err, "解析分页响应失败")

				// 验证角色数量
				assert.Equal(t, tt.expectedCount, len(paginatedResponse.Items), "角色数量应该匹配")
				assert.Equal(t, int64(tt.expectedCount), paginatedResponse.Total, "总数应该匹配")

				// 如果指定了角色类型，验证返回的角色类型
				if tt.characterType != "" {
					charactersBytes, err := json.Marshal(paginatedResponse.Items)
					require.NoError(t, err, "序列化角色列表失败")

					var characters []models.Character
					err = json.Unmarshal(charactersBytes, &characters)
					require.NoError(t, err, "解析角色列表失败")

					for _, character := range characters {
						assert.Equal(t, tt.characterType, character.CharacterType, "角色类型应该匹配过滤条件")
					}
				}
			} else {
				// 验证错误响应
				assert.False(t, response.Success, "响应应该标记为失败")
				assert.NotEmpty(t, response.Message, "错误消息不应该为空")
			}
		})
	}
}

func TestGameHandler_GetWorldCharacters_Pagination(t *testing.T) {
	testutil.SkipIfNoDatabase(t)

	handler, router, testDB := setupGameHandler(t)
	defer testDB.CleanupTestDB(t)

	// 创建测试数据
	user := testDB.CreateTestUser(t)
	world := testDB.CreateTestWorld(t, user.ID)

	// 创建多个测试角色
	const characterCount = 15
	for i := 0; i < characterCount; i++ {
		character := &models.Character{
			WorldID:       world.ID,
			UserID:        &user.ID,
			Name:          fmt.Sprintf("测试角色_%d", i+1),
			Description:   testutil.StringPtr(fmt.Sprintf("第%d个测试角色", i+1)),
			CharacterType: "player",
			Status:        "active",
		}
		err := testDB.DB.Create(character).Error
		require.NoError(t, err, "创建测试角色失败")
	}

	// 设置路由
	router.GET("/game/worlds/:world_id/characters", handler.GetWorldCharacters)

	t.Run("分页功能测试", func(t *testing.T) {
		// 第一页，每页5个
		req, err := http.NewRequest("GET", fmt.Sprintf("/game/worlds/%s/characters?page=1&limit=5", world.ID.String()), nil)
		require.NoError(t, err)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response Response
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		dataBytes, err := json.Marshal(response.Data)
		require.NoError(t, err)

		var paginatedResponse PaginatedResponse
		err = json.Unmarshal(dataBytes, &paginatedResponse)
		require.NoError(t, err)

		// 验证分页信息
		assert.Equal(t, 5, len(paginatedResponse.Items), "第一页应该返回5个角色")
		assert.Equal(t, int64(characterCount), paginatedResponse.Total, "总数应该正确")
		assert.Equal(t, 1, paginatedResponse.Page, "页码应该正确")
		assert.Equal(t, 5, paginatedResponse.Limit, "每页限制应该正确")
		assert.Equal(t, int64(3), paginatedResponse.TotalPages, "总页数应该正确")
	})

	t.Run("最后一页测试", func(t *testing.T) {
		// 最后一页，应该只有5个角色
		req, err := http.NewRequest("GET", fmt.Sprintf("/game/worlds/%s/characters?page=3&limit=5", world.ID.String()), nil)
		require.NoError(t, err)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response Response
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		dataBytes, err := json.Marshal(response.Data)
		require.NoError(t, err)

		var paginatedResponse PaginatedResponse
		err = json.Unmarshal(dataBytes, &paginatedResponse)
		require.NoError(t, err)

		// 验证最后一页
		assert.Equal(t, 5, len(paginatedResponse.Items), "最后一页应该返回5个角色")
		assert.Equal(t, 3, paginatedResponse.Page, "页码应该正确")
	})
}
