package handlers

import (
	"testing"

	"ai-text-game-iam-npc/pkg/logger"

	"github.com/stretchr/testify/assert"
)

// TestAIHandler_BuildScenePrompt 测试场景提示词构建
func TestAIHandler_BuildScenePrompt(t *testing.T) {
	// 创建一个真实的AIHandler实例用于测试辅助方法
	handler := &AIHandler{
		logger: logger.New("test-ai-handler"),
	}

	tests := []struct {
		name        string
		request     GenerateSceneRequest
		expected    string
		description string
	}{
		{
			name: "完整参数构建提示词",
			request: GenerateSceneRequest{
				SceneName:           "神秘森林",
				SceneType:           "main",
				Theme:               "fantasy",
				Mood:                "dark",
				ConnectedScenes:     []string{"村庄", "山洞"},
				SpecialRequirements: "包含魔法元素",
			},
			expected:    "请生成一个游戏场景的详细描述。场景名称：神秘森林。主题风格：fantasy。场景类型：main。氛围设定：dark。特殊要求：包含魔法元素。需要连接的场景：村庄、山洞。请生成包含场景名称、详细描述、氛围、关键特征和可能的行动的完整场景信息。",
			description: "使用完整参数构建详细提示词",
		},
		{
			name: "使用旧格式提示词",
			request: GenerateSceneRequest{
				Prompt: "生成一个神秘的森林场景",
			},
			expected:    "生成一个神秘的森林场景",
			description: "直接使用旧格式的提示词",
		},
		{
			name: "最小参数构建提示词",
			request: GenerateSceneRequest{
				SceneName: "简单场景",
			},
			expected:    "请生成一个游戏场景的详细描述。场景名称：简单场景。请生成包含场景名称、详细描述、氛围、关键特征和可能的行动的完整场景信息。",
			description: "使用最小参数构建基础提示词",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := handler.buildScenePrompt(tt.request)
			assert.Equal(t, tt.expected, result, tt.description)
		})
	}
}

// TestAIHandler_BuildSceneContext 测试场景上下文构建
func TestAIHandler_BuildSceneContext(t *testing.T) {
	// 创建一个真实的AIHandler实例用于测试辅助方法
	handler := &AIHandler{
		logger: logger.New("test-ai-handler"),
	}

	tests := []struct {
		name        string
		request     GenerateSceneRequest
		expected    map[string]interface{}
		description string
	}{
		{
			name: "构建完整上下文",
			request: GenerateSceneRequest{
				WorldID:             "test-world",
				SceneName:           "测试场景",
				SceneType:           "special",
				Theme:               "sci-fi",
				Mood:                "tense",
				ConnectedScenes:     []string{"场景1", "场景2"},
				SpecialRequirements: "包含机器人",
			},
			expected: map[string]interface{}{
				"world_id":             "test-world",
				"scene_name":           "测试场景",
				"scene_type":           "special",
				"theme":                "sci-fi",
				"mood":                 "tense",
				"connected_scenes":     []string{"场景1", "场景2"},
				"special_requirements": "包含机器人",
			},
			description: "构建包含所有字段的上下文",
		},
		{
			name: "合并旧格式上下文",
			request: GenerateSceneRequest{
				WorldID:   "test-world",
				SceneName: "新场景",
				Context: map[string]interface{}{
					"existing_key": "existing_value",
					"theme":        "old_theme", // 应该被新值覆盖
				},
			},
			expected: map[string]interface{}{
				"world_id":     "test-world",
				"scene_name":   "新场景",
				"existing_key": "existing_value",
				"theme":        "old_theme", // 旧值保留，因为新格式中没有theme
			},
			description: "正确合并旧格式和新格式的上下文",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := handler.buildSceneContext(tt.request)

			// 验证所有期望的键值对都存在
			for key, expectedValue := range tt.expected {
				actualValue, exists := result[key]
				assert.True(t, exists, "上下文应该包含键: %s", key)
				assert.Equal(t, expectedValue, actualValue, "键 %s 的值不匹配", key)
			}
		})
	}
}