package ai

import (
	"context"
	"fmt"
	"sync"
	"time"

	"ai-text-game-iam-npc/internal/models"
	"ai-text-game-iam-npc/pkg/logger"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

/**
 * 异步任务管理器
 * @description 管理AI生成任务的异步执行和状态跟踪
 */

// TaskStatus 任务状态枚举
type TaskStatus string

const (
	TaskStatusPending    TaskStatus = "pending"     // 等待中
	TaskStatusRunning    TaskStatus = "running"     // 执行中
	TaskStatusCompleted  TaskStatus = "completed"   // 已完成
	TaskStatusFailed     TaskStatus = "failed"      // 失败
	TaskStatusCancelled  TaskStatus = "cancelled"   // 已取消
	TaskStatusTimeout    TaskStatus = "timeout"     // 超时
)

// AsyncTask 异步任务
type AsyncTask struct {
	ID          string                 `json:"id"`           // 任务ID
	Type        string                 `json:"type"`         // 任务类型
	Status      TaskStatus             `json:"status"`       // 任务状态
	Request     *GenerateRequest       `json:"request"`      // 原始请求
	Response    *GenerateResponse      `json:"response"`     // 响应结果
	Error       string                 `json:"error"`        // 错误信息
	Progress    float64                `json:"progress"`     // 进度百分比
	CreatedAt   time.Time              `json:"created_at"`   // 创建时间
	StartedAt   *time.Time             `json:"started_at"`   // 开始时间
	CompletedAt *time.Time             `json:"completed_at"` // 完成时间
	Context     map[string]interface{} `json:"context"`      // 上下文信息
	
	// 内部字段
	ctx        context.Context    `json:"-"`
	cancelFunc context.CancelFunc `json:"-"`
	callbacks  []TaskCallback     `json:"-"`
}

// TaskCallback 任务回调函数
type TaskCallback func(task *AsyncTask)

// AsyncTaskManager 异步任务管理器
type AsyncTaskManager struct {
	tasks       map[string]*AsyncTask
	taskQueue   chan *AsyncTask
	workerCount int
	mutex       sync.RWMutex
	db          *gorm.DB
	aiService   *Service
	logger      logger.Logger
	
	// 统计信息
	stats struct {
		TotalTasks     int64 `json:"total_tasks"`
		CompletedTasks int64 `json:"completed_tasks"`
		FailedTasks    int64 `json:"failed_tasks"`
		ActiveTasks    int64 `json:"active_tasks"`
	}
}

// NewAsyncTaskManager 创建异步任务管理器
func NewAsyncTaskManager(db *gorm.DB, aiService *Service, workerCount int, queueSize int, log logger.Logger) *AsyncTaskManager {
	if workerCount <= 0 {
		workerCount = 5 // 默认5个工作协程
	}
	if queueSize <= 0 {
		queueSize = 100 // 默认队列大小100
	}
	
	manager := &AsyncTaskManager{
		tasks:       make(map[string]*AsyncTask),
		taskQueue:   make(chan *AsyncTask, queueSize),
		workerCount: workerCount,
		db:          db,
		aiService:   aiService,
		logger:      log,
	}
	
	// 启动工作协程
	manager.startWorkers()
	
	return manager
}

// startWorkers 启动工作协程
func (atm *AsyncTaskManager) startWorkers() {
	for i := 0; i < atm.workerCount; i++ {
		go atm.worker(i)
	}
	atm.logger.Info("异步任务管理器已启动", "worker_count", atm.workerCount)
}

// worker 工作协程
func (atm *AsyncTaskManager) worker(workerID int) {
	atm.logger.Debug("工作协程启动", "worker_id", workerID)
	
	for task := range atm.taskQueue {
		atm.logger.Debug("工作协程处理任务", "worker_id", workerID, "task_id", task.ID)
		atm.executeTask(task)
	}
	
	atm.logger.Debug("工作协程退出", "worker_id", workerID)
}

// SubmitTask 提交异步任务
func (atm *AsyncTaskManager) SubmitTask(req *GenerateRequest) (*AsyncTask, error) {
	// 创建任务
	task := &AsyncTask{
		ID:        uuid.New().String(),
		Type:      req.Type,
		Status:    TaskStatusPending,
		Request:   req,
		Progress:  0.0,
		CreatedAt: time.Now(),
		Context:   make(map[string]interface{}),
	}
	
	// 创建上下文
	task.ctx, task.cancelFunc = context.WithTimeout(context.Background(), 5*time.Minute)
	
	// 存储任务
	atm.mutex.Lock()
	atm.tasks[task.ID] = task
	atm.stats.TotalTasks++
	atm.stats.ActiveTasks++
	atm.mutex.Unlock()
	
	// 提交到队列
	select {
	case atm.taskQueue <- task:
		atm.logger.Info("任务已提交到队列", "task_id", task.ID, "type", task.Type)
		return task, nil
	default:
		// 队列已满
		atm.mutex.Lock()
		delete(atm.tasks, task.ID)
		atm.stats.TotalTasks--
		atm.stats.ActiveTasks--
		atm.mutex.Unlock()
		
		task.cancelFunc()
		return nil, fmt.Errorf("任务队列已满，无法提交任务")
	}
}

// executeTask 执行任务
func (atm *AsyncTaskManager) executeTask(task *AsyncTask) {
	// 更新任务状态
	atm.updateTaskStatus(task, TaskStatusRunning)
	now := time.Now()
	task.StartedAt = &now
	task.Progress = 10.0
	
	atm.logger.Info("开始执行任务", "task_id", task.ID, "type", task.Type)
	
	// 执行AI生成
	response, err := atm.aiService.GenerateContentWithValidation(task.ctx, task.Request)
	
	if err != nil {
		// 任务失败
		atm.handleTaskFailure(task, err)
		return
	}
	
	// 任务成功
	atm.handleTaskSuccess(task, response)
}

// updateTaskStatus 更新任务状态
func (atm *AsyncTaskManager) updateTaskStatus(task *AsyncTask, status TaskStatus) {
	atm.mutex.Lock()
	defer atm.mutex.Unlock()
	
	oldStatus := task.Status
	task.Status = status
	
	// 更新统计信息
	if oldStatus == TaskStatusRunning && status != TaskStatusRunning {
		atm.stats.ActiveTasks--
	}
	
	if status == TaskStatusCompleted {
		atm.stats.CompletedTasks++
	} else if status == TaskStatusFailed || status == TaskStatusTimeout {
		atm.stats.FailedTasks++
	}
	
	atm.logger.Debug("任务状态更新", "task_id", task.ID, "old_status", oldStatus, "new_status", status)
}

// handleTaskSuccess 处理任务成功
func (atm *AsyncTaskManager) handleTaskSuccess(task *AsyncTask, response *GenerateResponse) {
	task.Response = response
	task.Progress = 100.0
	now := time.Now()
	task.CompletedAt = &now
	
	atm.updateTaskStatus(task, TaskStatusCompleted)
	
	// 保存到数据库
	if err := atm.saveTaskToDatabase(task); err != nil {
		atm.logger.Error("保存任务到数据库失败", "task_id", task.ID, "error", err)
	}
	
	// 执行回调
	atm.executeCallbacks(task)
	
	atm.logger.Info("任务执行成功", "task_id", task.ID, "type", task.Type, 
		"duration", time.Since(task.CreatedAt))
}

// handleTaskFailure 处理任务失败
func (atm *AsyncTaskManager) handleTaskFailure(task *AsyncTask, err error) {
	task.Error = err.Error()
	now := time.Now()
	task.CompletedAt = &now
	
	// 判断失败类型
	if task.ctx.Err() == context.DeadlineExceeded {
		atm.updateTaskStatus(task, TaskStatusTimeout)
	} else if task.ctx.Err() == context.Canceled {
		atm.updateTaskStatus(task, TaskStatusCancelled)
	} else {
		atm.updateTaskStatus(task, TaskStatusFailed)
	}
	
	// 保存到数据库
	if dbErr := atm.saveTaskToDatabase(task); dbErr != nil {
		atm.logger.Error("保存失败任务到数据库失败", "task_id", task.ID, "error", dbErr)
	}
	
	// 执行回调
	atm.executeCallbacks(task)
	
	atm.logger.Error("任务执行失败", "task_id", task.ID, "type", task.Type, 
		"error", err, "duration", time.Since(task.CreatedAt))
}

// saveTaskToDatabase 保存任务到数据库
func (atm *AsyncTaskManager) saveTaskToDatabase(task *AsyncTask) error {
	// 创建AI交互记录
	interaction := &models.AIInteraction{
		WorldID:         convertUUIDToStringPtr(task.Request.WorldID),
		UserID:          convertUUIDToStringPtr(task.Request.UserID),
		InteractionType: task.Type,
		Prompt:          task.Request.Prompt,
		Status:          string(task.Status),
	}
	
	if task.Request.Schema != nil {
		interaction.ResponseSchema = models.JSON(task.Request.Schema)
	}
	
	if task.Response != nil {
		interaction.Response = &task.Response.Content
		interaction.TokenUsage = &task.Response.TokenUsage
		interaction.ResponseTime = &task.Response.ResponseTime
	}
	
	if task.Error != "" {
		interaction.ErrorMessage = &task.Error
	}
	
	return atm.db.Create(interaction).Error
}

// convertUUIDToStringPtr 将 *uuid.UUID 转换为 *string
func convertUUIDToStringPtr(uuidPtr *uuid.UUID) *string {
	if uuidPtr == nil {
		return nil
	}
	str := uuidPtr.String()
	return &str
}

// convertUUIDToString 将 uuid.UUID 转换为 string
func convertUUIDToString(uuid uuid.UUID) string {
	return uuid.String()
}

// executeCallbacks 执行回调函数
func (atm *AsyncTaskManager) executeCallbacks(task *AsyncTask) {
	for _, callback := range task.callbacks {
		go func(cb TaskCallback) {
			defer func() {
				if r := recover(); r != nil {
					atm.logger.Error("任务回调执行失败", "task_id", task.ID, "panic", r)
				}
			}()
			cb(task)
		}(callback)
	}
}

// GetTask 获取任务信息
func (atm *AsyncTaskManager) GetTask(taskID string) (*AsyncTask, error) {
	atm.mutex.RLock()
	defer atm.mutex.RUnlock()
	
	task, exists := atm.tasks[taskID]
	if !exists {
		return nil, fmt.Errorf("任务不存在: %s", taskID)
	}
	
	// 返回任务的副本，避免外部修改
	taskCopy := *task
	return &taskCopy, nil
}

// CancelTask 取消任务
func (atm *AsyncTaskManager) CancelTask(taskID string) error {
	atm.mutex.Lock()
	defer atm.mutex.Unlock()
	
	task, exists := atm.tasks[taskID]
	if !exists {
		return fmt.Errorf("任务不存在: %s", taskID)
	}
	
	if task.Status == TaskStatusCompleted || task.Status == TaskStatusFailed {
		return fmt.Errorf("任务已完成，无法取消: %s", taskID)
	}
	
	// 取消任务
	task.cancelFunc()
	atm.updateTaskStatus(task, TaskStatusCancelled)
	
	atm.logger.Info("任务已取消", "task_id", taskID)
	return nil
}

// AddTaskCallback 添加任务回调
func (atm *AsyncTaskManager) AddTaskCallback(taskID string, callback TaskCallback) error {
	atm.mutex.Lock()
	defer atm.mutex.Unlock()
	
	task, exists := atm.tasks[taskID]
	if !exists {
		return fmt.Errorf("任务不存在: %s", taskID)
	}
	
	task.callbacks = append(task.callbacks, callback)
	return nil
}

// GetTaskList 获取任务列表
func (atm *AsyncTaskManager) GetTaskList(limit int, offset int) ([]*AsyncTask, int, error) {
	atm.mutex.RLock()
	defer atm.mutex.RUnlock()
	
	// 转换为切片
	allTasks := make([]*AsyncTask, 0, len(atm.tasks))
	for _, task := range atm.tasks {
		taskCopy := *task
		allTasks = append(allTasks, &taskCopy)
	}
	
	total := len(allTasks)
	
	// 分页
	if offset >= total {
		return []*AsyncTask{}, total, nil
	}
	
	end := offset + limit
	if end > total {
		end = total
	}
	
	return allTasks[offset:end], total, nil
}

// GetStatistics 获取统计信息
func (atm *AsyncTaskManager) GetStatistics() map[string]interface{} {
	atm.mutex.RLock()
	defer atm.mutex.RUnlock()
	
	return map[string]interface{}{
		"total_tasks":     atm.stats.TotalTasks,
		"completed_tasks": atm.stats.CompletedTasks,
		"failed_tasks":    atm.stats.FailedTasks,
		"active_tasks":    atm.stats.ActiveTasks,
		"queue_size":      len(atm.taskQueue),
		"worker_count":    atm.workerCount,
	}
}

// Cleanup 清理已完成的任务
func (atm *AsyncTaskManager) Cleanup(maxAge time.Duration) int {
	atm.mutex.Lock()
	defer atm.mutex.Unlock()
	
	cutoff := time.Now().Add(-maxAge)
	cleaned := 0
	
	for taskID, task := range atm.tasks {
		if task.CompletedAt != nil && task.CompletedAt.Before(cutoff) {
			if task.cancelFunc != nil {
				task.cancelFunc()
			}
			delete(atm.tasks, taskID)
			cleaned++
		}
	}
	
	atm.logger.Info("任务清理完成", "cleaned_count", cleaned, "max_age", maxAge)
	return cleaned
}
