package ai

import (
	"testing"

	"github.com/google/uuid"
)

/**
 * 数据转换器单元测试
 * @description 测试AI生成数据到数据模型的转换功能
 */

func TestDataTransformer(t *testing.T) {
	// 创建测试用的logger
	log := newTestLogger()
	transformer := NewDataTransformer(log)
	
	t.Run("测试场景数据转换", func(t *testing.T) {
		worldID := uuid.New()
		
		// 模拟AI生成的场景数据
		sceneData := map[string]interface{}{
			"name":        "神秘森林",
			"description": "一片充满魔法气息的古老森林，高大的树木遮天蔽日，林间弥漫着淡淡的雾气。",
			"atmosphere":  "神秘",
			"scene_type":  "森林",
			"key_features": []interface{}{
				"古老的橡树",
				"发光的蘑菇",
				"潺潺小溪",
			},
			"possible_actions": []interface{}{
				"探索树林",
				"采集蘑菇",
				"跟随小径",
			},
			"connections": []interface{}{
				map[string]interface{}{
					"direction":    "北",
					"description":  "通往山脉的小径",
					"scene_name":   "雪山脚下",
					"is_locked":    false,
				},
				map[string]interface{}{
					"direction":        "东",
					"description":      "被荆棘封锁的道路",
					"scene_name":       "废弃神庙",
					"is_locked":        true,
					"unlock_condition": "需要魔法钥匙",
				},
			},
			"hidden_elements": []interface{}{
				"隐藏的宝箱",
				"精灵的踪迹",
			},
			"danger_level": 3,
			"lighting":     "昏暗",
			"sounds": []interface{}{
				"鸟儿啁啾",
				"树叶沙沙",
			},
			"smells": []interface{}{
				"泥土芬芳",
				"花香",
			},
		}
		
		scene, err := transformer.TransformToScene(sceneData, worldID)
		if err != nil {
			t.Fatalf("场景数据转换失败: %v", err)
		}
		
		// 验证基本信息
		if scene.Name != "神秘森林" {
			t.Errorf("期望场景名称为 '神秘森林'，实际为 '%s'", scene.Name)
		}
		
		if scene.WorldID != worldID {
			t.Errorf("期望世界ID为 %s，实际为 %s", worldID, scene.WorldID)
		}
		
		if scene.Description == nil || *scene.Description != "一片充满魔法气息的古老森林，高大的树木遮天蔽日，林间弥漫着淡淡的雾气。" {
			t.Error("场景描述转换错误")
		}
		
		// 验证属性
		properties := map[string]interface{}(scene.Properties)
		if properties["atmosphere"] != "神秘" {
			t.Errorf("期望氛围为 '神秘'，实际为 %v", properties["atmosphere"])
		}
		
		if properties["danger_level"] != 3 {
			t.Errorf("期望危险等级为 3，实际为 %v", properties["danger_level"])
		}
		
		// 验证连接信息
		connections := map[string]interface{}(scene.ConnectedScenes)
		if len(connections) != 2 {
			t.Errorf("期望连接数量为 2，实际为 %d", len(connections))
		}
	})
	
	t.Run("测试角色数据转换", func(t *testing.T) {
		worldID := uuid.New()
		userID := uuid.New()
		
		// 模拟AI生成的角色数据
		characterData := map[string]interface{}{
			"name":           "艾莉娅",
			"description":    "一位年轻的精灵法师，拥有银色的长发和翠绿的眼睛。",
			"character_type": "NPC",
			"personality": []interface{}{
				"聪明",
				"好奇",
				"善良",
			},
			"background": "艾莉娅出生在精灵王国，从小就展现出强大的魔法天赋。",
			"skills": []interface{}{
				"火球术",
				"治疗术",
				"传送术",
			},
			"dialogue_style": "温和而优雅，喜欢使用古老的精灵语",
			"motivations": []interface{}{
				"保护森林",
				"寻找失落的魔法",
			},
			"fears": []interface{}{
				"黑暗魔法",
				"失去朋友",
			},
			"goals": []interface{}{
				"成为大法师",
				"恢复精灵王国",
			},
			"relationships": []interface{}{
				"精灵长老的学生",
				"森林守护者的盟友",
			},
			"attributes": map[string]interface{}{
				"strength":     60,
				"intelligence": 95,
				"agility":      80,
				"charisma":     85,
				"health":       150,
				"mana":         200,
			},
			"appearance": map[string]interface{}{
				"height":               "中等身高",
				"build":                "苗条",
				"hair_color":           "银色",
				"eye_color":            "翠绿",
				"skin_tone":            "白皙",
				"distinctive_features": []interface{}{"尖耳朵", "魔法印记"},
				"clothing_style":       "精灵法师长袍",
			},
			"age_range":  "青年",
			"occupation": "法师",
			"alignment":  "善良",
		}
		
		character, err := transformer.TransformToCharacter(characterData, worldID, &userID)
		if err != nil {
			t.Fatalf("角色数据转换失败: %v", err)
		}
		
		// 验证基本信息
		if character.Name != "艾莉娅" {
			t.Errorf("期望角色名称为 '艾莉娅'，实际为 '%s'", character.Name)
		}
		
		if character.WorldID != worldID {
			t.Errorf("期望世界ID为 %s，实际为 %s", worldID, character.WorldID)
		}
		
		if character.UserID == nil || *character.UserID != userID {
			t.Error("用户ID转换错误")
		}
		
		if character.CharacterType != "npc" {
			t.Errorf("期望角色类型为 'npc'，实际为 '%s'", character.CharacterType)
		}
		
		// 验证特质
		traits := []string(character.Traits)
		if len(traits) != 3 {
			t.Errorf("期望特质数量为 3，实际为 %d", len(traits))
		}
		
		// 验证属性
		experiences := map[string]interface{}(character.Experiences)
		if experiences["occupation"] != "法师" {
			t.Errorf("期望职业为 '法师'，实际为 %v", experiences["occupation"])
		}
	})
	
	t.Run("测试事件数据转换", func(t *testing.T) {
		worldID := uuid.New()
		creatorID := uuid.New()
		
		// 模拟AI生成的事件数据
		eventData := map[string]interface{}{
			"name":        "神秘商人的到来",
			"description": "一位神秘的商人突然出现在村庄广场，他的马车装满了奇异的物品。",
			"event_type":  "随机遭遇",
			"priority":    5,
			"duration":    60,
			"trigger_conditions": []interface{}{
				"玩家在村庄",
				"白天时间",
			},
			"participants": []interface{}{
				"神秘商人",
				"村民",
			},
			"location_requirements": []interface{}{
				"村庄广场",
			},
			"effects": map[string]interface{}{
				"description": "玩家可以购买稀有物品",
				"consequences": []interface{}{
					"获得新的交易机会",
					"了解外界信息",
				},
				"rewards": []interface{}{
					"稀有物品",
					"金币",
				},
				"penalties": []interface{}{},
				"stat_changes": map[string]interface{}{
					"experience": 10,
				},
			},
			"choices": []interface{}{
				map[string]interface{}{
					"text":        "与商人交谈",
					"consequence": "开启交易界面",
				},
				map[string]interface{}{
					"text":        "观察商人",
					"consequence": "获得商人信息",
				},
			},
			"difficulty":  "普通",
			"repeatable":  false,
		}
		
		event, err := transformer.TransformToEvent(eventData, worldID, &creatorID)
		if err != nil {
			t.Fatalf("事件数据转换失败: %v", err)
		}
		
		// 验证基本信息
		if event.Name == nil || *event.Name != "神秘商人的到来" {
			t.Error("事件名称转换错误")
		}
		
		if event.WorldID != worldID {
			t.Errorf("期望世界ID为 %s，实际为 %s", worldID, event.WorldID)
		}
		
		if event.CreatorID == nil || *event.CreatorID != creatorID {
			t.Error("创建者ID转换错误")
		}
		
		if event.EventType != "encounter" {
			t.Errorf("期望事件类型为 'encounter'，实际为 '%s'", event.EventType)
		}
		
		if event.Priority != 5 {
			t.Errorf("期望优先级为 5，实际为 %d", event.Priority)
		}
		
		// 验证事件数据
		eventDataMap := map[string]interface{}(event.EventData)
		if eventDataMap["difficulty"] != "普通" {
			t.Errorf("期望难度为 '普通'，实际为 %v", eventDataMap["difficulty"])
		}
	})
	
	t.Run("测试类型映射", func(t *testing.T) {
		// 测试场景类型映射
		sceneType := transformer.mapSceneType("室内")
		if sceneType != "indoor" {
			t.Errorf("期望场景类型映射为 'indoor'，实际为 '%s'", sceneType)
		}
		
		// 测试未知场景类型
		unknownSceneType := transformer.mapSceneType("未知类型")
		if unknownSceneType != "normal" {
			t.Errorf("期望未知场景类型映射为 'normal'，实际为 '%s'", unknownSceneType)
		}
		
		// 测试角色类型映射
		characterType := transformer.mapCharacterType("商人")
		if characterType != "merchant" {
			t.Errorf("期望角色类型映射为 'merchant'，实际为 '%s'", characterType)
		}
		
		// 测试事件类型映射
		eventType := transformer.mapEventType("战斗")
		if eventType != "combat" {
			t.Errorf("期望事件类型映射为 'combat'，实际为 '%s'", eventType)
		}
	})
	
	t.Run("测试错误处理", func(t *testing.T) {
		worldID := uuid.New()
		
		// 测试缺少必需字段的场景数据
		invalidSceneData := map[string]interface{}{
			"description": "缺少名称的场景",
		}
		
		_, err := transformer.TransformToScene(invalidSceneData, worldID)
		if err == nil {
			t.Error("期望缺少名称的场景数据转换失败")
		}
		
		// 测试缺少必需字段的角色数据
		invalidCharacterData := map[string]interface{}{
			"description": "缺少名称的角色",
		}
		
		_, err = transformer.TransformToCharacter(invalidCharacterData, worldID, nil)
		if err == nil {
			t.Error("期望缺少名称的角色数据转换失败")
		}
		
		// 测试缺少必需字段的事件数据
		invalidEventData := map[string]interface{}{
			"description": "缺少名称的事件",
		}
		
		_, err = transformer.TransformToEvent(invalidEventData, worldID, nil)
		if err == nil {
			t.Error("期望缺少名称的事件数据转换失败")
		}
	})
}
