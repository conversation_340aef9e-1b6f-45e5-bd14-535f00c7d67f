package models

import (
	"database/sql/driver"
	"encoding/json"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Scene 场景模型 - 兼容SQLite和PostgreSQL
type Scene struct {
	// 使用string类型存储UUID，兼容SQLite
	ID              string         `json:"id" gorm:"primaryKey;type:text"`
	WorldID         string         `json:"world_id" gorm:"type:text;not null;index"`
	Name            string         `json:"name" gorm:"not null;size:200"`
	Description     *string        `json:"description"`
	SceneType       string         `json:"scene_type" gorm:"default:'normal';index"` // normal, special, hidden
	// 使用text类型存储JSON，兼容SQLite
	Properties      JSON           `json:"properties" gorm:"type:text;default:'{}'"`
	EntitiesPresent StringArray    `json:"entities_present" gorm:"type:text;default:'[]'"` // 当前场景中的实体ID列表
	ConnectedScenes JSON           `json:"connected_scenes" gorm:"type:text;default:'{}'"` // 连接的场景
	Environment     JSON           `json:"environment" gorm:"type:text;default:'{}'"`      // 环境信息
	Status          string         `json:"status" gorm:"default:'active';index"`            // active, locked, hidden
	CreatedAt       time.Time      `json:"created_at"`
	UpdatedAt       time.Time      `json:"updated_at"`
	DeletedAt       gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`

	// 关联
	World      World       `json:"world,omitempty" gorm:"foreignKey:WorldID"`
	Characters []Character `json:"characters,omitempty" gorm:"foreignKey:CurrentSceneID"`
	Entities   []Entity    `json:"entities,omitempty" gorm:"foreignKey:CurrentSceneID"`
	Events     []Event     `json:"events,omitempty" gorm:"foreignKey:SceneID"`
}

// UUIDArray UUID数组类型
type UUIDArray []uuid.UUID

// Scan 实现Scanner接口
func (ua *UUIDArray) Scan(value interface{}) error {
	if value == nil {
		*ua = UUIDArray{}
		return nil
	}

	var strArray []string
	switch v := value.(type) {
	case []byte:
		if err := json.Unmarshal(v, &strArray); err != nil {
			return err
		}
	case string:
		if err := json.Unmarshal([]byte(v), &strArray); err != nil {
			return err
		}
	default:
		*ua = UUIDArray{}
		return nil
	}

	// 转换字符串数组为UUID数组
	result := make(UUIDArray, 0, len(strArray))
	for _, str := range strArray {
		if id, err := uuid.Parse(str); err == nil {
			result = append(result, id)
		}
	}
	*ua = result
	return nil
}

// Value 实现Valuer接口
func (ua UUIDArray) Value() (driver.Value, error) {
	if len(ua) == 0 {
		return "[]", nil
	}

	// 转换UUID数组为字符串数组
	strArray := make([]string, len(ua))
	for i, id := range ua {
		strArray[i] = id.String()
	}

	return json.Marshal(strArray)
}

// SceneEnvironment 场景环境结构
type SceneEnvironment struct {
	Weather     string                 `json:"weather"`     // 天气
	Temperature int                    `json:"temperature"` // 温度
	Lighting    string                 `json:"lighting"`    // 光照
	Sounds      []string               `json:"sounds"`      // 声音
	Smells      []string               `json:"smells"`      // 气味
	Atmosphere  string                 `json:"atmosphere"`  // 氛围
	Hazards     []string               `json:"hazards"`     // 危险因素
	Resources   map[string]interface{} `json:"resources"`   // 可用资源
}

// TableName 指定表名
func (Scene) TableName() string {
	return "scenes"
}

// BeforeCreate GORM钩子 - 创建前
func (s *Scene) BeforeCreate(tx *gorm.DB) error {
	if s.ID == "" {
		s.ID = uuid.New().String()
	}

	// 设置默认环境
	if len(s.Environment) == 0 {
		s.Environment = JSON{
			"weather":     "clear",
			"temperature": 20,
			"lighting":    "normal",
			"sounds":      []string{},
			"smells":      []string{},
			"atmosphere":  "neutral",
			"hazards":     []string{},
			"resources":   map[string]interface{}{},
		}
	}

	return nil
}

// IsActive 检查场景是否活跃
func (s *Scene) IsActive() bool {
	return s.Status == "active"
}

// IsLocked 检查场景是否被锁定
func (s *Scene) IsLocked() bool {
	return s.Status == "locked"
}

// IsHidden 检查场景是否隐藏
func (s *Scene) IsHidden() bool {
	return s.Status == "hidden" || s.SceneType == "hidden"
}

// AddEntity 添加实体到场景
func (s *Scene) AddEntity(tx *gorm.DB, entityID string) error {
	// 检查是否已存在
	for _, id := range s.EntitiesPresent {
		if id == entityID {
			return nil
		}
	}

	s.EntitiesPresent = append(s.EntitiesPresent, entityID)
	return tx.Model(s).Update("entities_present", s.EntitiesPresent).Error
}

// RemoveEntity 从场景移除实体
func (s *Scene) RemoveEntity(tx *gorm.DB, entityID string) error {
	filtered := make(StringArray, 0, len(s.EntitiesPresent))
	for _, id := range s.EntitiesPresent {
		if id != entityID {
			filtered = append(filtered, id)
		}
	}

	s.EntitiesPresent = filtered
	return tx.Model(s).Update("entities_present", s.EntitiesPresent).Error
}

// HasEntity 检查场景是否包含指定实体
func (s *Scene) HasEntity(entityID string) bool {
	for _, id := range s.EntitiesPresent {
		if id == entityID {
			return true
		}
	}
	return false
}

// ConnectScene 连接到另一个场景
func (s *Scene) ConnectScene(tx *gorm.DB, direction string, targetSceneID string) error {
	connections := s.GetConnections()
	connections[direction] = targetSceneID

	// 转换 map[string]string 到 JSON (map[string]interface{})
	jsonConnections := make(JSON)
	for k, v := range connections {
		jsonConnections[k] = v
	}
	s.ConnectedScenes = jsonConnections
	return tx.Model(s).Update("connected_scenes", s.ConnectedScenes).Error
}

// DisconnectScene 断开与另一个场景的连接
func (s *Scene) DisconnectScene(tx *gorm.DB, direction string) error {
	connections := s.GetConnections()
	delete(connections, direction)

	// 转换 map[string]string 到 JSON (map[string]interface{})
	jsonConnections := make(JSON)
	for k, v := range connections {
		jsonConnections[k] = v
	}
	s.ConnectedScenes = jsonConnections
	return tx.Model(s).Update("connected_scenes", s.ConnectedScenes).Error
}

// GetConnections 获取场景连接
func (s *Scene) GetConnections() map[string]string {
	connections := make(map[string]string)
	for key, value := range s.ConnectedScenes {
		if str, ok := value.(string); ok {
			connections[key] = str
		}
	}
	return connections
}

// GetConnectedSceneID 获取指定方向的连接场景ID
func (s *Scene) GetConnectedSceneID(direction string) *uuid.UUID {
	connections := s.GetConnections()
	if sceneIDStr, ok := connections[direction]; ok {
		if sceneID, err := uuid.Parse(sceneIDStr); err == nil {
			return &sceneID
		}
	}
	return nil
}

// GetAvailableDirections 获取可用的移动方向
func (s *Scene) GetAvailableDirections() []string {
	connections := s.GetConnections()
	directions := make([]string, 0, len(connections))
	for direction := range connections {
		directions = append(directions, direction)
	}
	return directions
}

// UpdateEnvironment 更新环境信息
func (s *Scene) UpdateEnvironment(tx *gorm.DB, updates map[string]interface{}) error {
	// 合并更新
	for key, value := range updates {
		s.Environment[key] = value
	}

	return tx.Model(s).Update("environment", s.Environment).Error
}

// GetEnvironmentValue 获取环境值
func (s *Scene) GetEnvironmentValue(key string) interface{} {
	return s.Environment[key]
}

// SetProperty 设置场景属性
func (s *Scene) SetProperty(tx *gorm.DB, key string, value interface{}) error {
	s.Properties[key] = value
	return tx.Model(s).Update("properties", s.Properties).Error
}

// GetProperty 获取场景属性
func (s *Scene) GetProperty(key string) interface{} {
	return s.Properties[key]
}

// HasProperty 检查是否有指定属性
func (s *Scene) HasProperty(key string) bool {
	_, exists := s.Properties[key]
	return exists
}

// GetWeather 获取天气
func (s *Scene) GetWeather() string {
	if weather, ok := s.Environment["weather"].(string); ok {
		return weather
	}
	return "clear"
}

// GetTemperature 获取温度
func (s *Scene) GetTemperature() int {
	if temp, ok := s.Environment["temperature"].(float64); ok {
		return int(temp)
	}
	return 20
}

// GetLighting 获取光照
func (s *Scene) GetLighting() string {
	if lighting, ok := s.Environment["lighting"].(string); ok {
		return lighting
	}
	return "normal"
}

// GetAtmosphere 获取氛围
func (s *Scene) GetAtmosphere() string {
	if atmosphere, ok := s.Environment["atmosphere"].(string); ok {
		return atmosphere
	}
	return "neutral"
}

// IsSpecial 检查是否为特殊场景
func (s *Scene) IsSpecial() bool {
	return s.SceneType == "special"
}

// CanEnter 检查是否可以进入场景
func (s *Scene) CanEnter() bool {
	return s.IsActive() && !s.IsLocked()
}

// GetEntityCount 获取场景中实体数量
func (s *Scene) GetEntityCount() int {
	return len(s.EntitiesPresent)
}
