package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Character 角色模型 - 兼容SQLite和PostgreSQL
type Character struct {
	// 使用string类型存储UUID，兼容SQLite
	ID             string         `json:"id" gorm:"primaryKey;type:text"`
	WorldID        string         `json:"world_id" gorm:"type:text;not null;index"`
	UserID         *string        `json:"user_id" gorm:"type:text;index"` // NULL表示NPC
	Name           string         `json:"name" gorm:"not null;size:100"`
	Description    *string        `json:"description"`
	CharacterType  string         `json:"character_type" gorm:"default:'player';index"` // player, npc, collective
	CurrentSceneID *string        `json:"current_scene_id" gorm:"type:text;index"`
	// 使用text类型存储JSON，兼容SQLite
	Traits         StringArray    `json:"traits" gorm:"type:text;default:'[]'"`        // 特质列表
	Memories       JSON           `json:"memories" gorm:"type:text;default:'[]'"`      // 记忆系统
	Experiences    JSON           `json:"experiences" gorm:"type:text;default:'{}'"`   // 阅历系统
	Relationships  JSON           `json:"relationships" gorm:"type:text;default:'{}'"` // 关系网络
	Status         string         `json:"status" gorm:"default:'active';index"`         // active, inactive, dead
	LastActionAt   *time.Time     `json:"last_action_at"`
	CreatedAt      time.Time      `json:"created_at"`
	UpdatedAt      time.Time      `json:"updated_at"`
	DeletedAt      gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`

	// 关联
	World         World    `json:"world,omitempty" gorm:"foreignKey:WorldID"`
	User          *User    `json:"user,omitempty" gorm:"foreignKey:UserID"`
	CurrentScene  *Scene   `json:"current_scene,omitempty" gorm:"foreignKey:CurrentSceneID"`
	OwnedEntities []Entity `json:"owned_entities,omitempty" gorm:"foreignKey:OwnerID"`
	CreatedEvents []Event  `json:"created_events,omitempty" gorm:"foreignKey:CreatorID"`
}

// Memory 记忆结构
type Memory struct {
	ID          uuid.UUID              `json:"id"`
	Type        string                 `json:"type"`        // event, person, place, item
	Content     string                 `json:"content"`     // 记忆内容
	Importance  int                    `json:"importance"`  // 重要性 (1-10)
	Clarity     float64                `json:"clarity"`     // 清晰度 (0.0-1.0)
	Tags        []string               `json:"tags"`        // 标签
	RelatedIDs  []uuid.UUID            `json:"related_ids"` // 相关实体ID
	CreatedAt   time.Time              `json:"created_at"`
	LastAccess  time.Time              `json:"last_access"`
	AccessCount int                    `json:"access_count"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// Experience 阅历结构
type Experience struct {
	Type        string    `json:"type"`     // combat, social, exploration, crafting, event
	Category    string    `json:"category"` // 具体分类
	Level       int       `json:"level"`    // 阅历等级
	Count       int       `json:"count"`    // 经历次数
	LastUpdated time.Time `json:"last_updated"`
	Details     []string  `json:"details"` // 具体经历描述
}

// Relationship 关系结构
type Relationship struct {
	TargetID     uuid.UUID              `json:"target_id"`     // 目标角色ID
	Type         string                 `json:"type"`          // friend, enemy, neutral, family, etc.
	Strength     int                    `json:"strength"`      // 关系强度 (-100 to 100)
	Trust        int                    `json:"trust"`         // 信任度 (0-100)
	History      []string               `json:"history"`       // 关系历史
	LastInteract time.Time              `json:"last_interact"` // 最后交互时间
	Metadata     map[string]interface{} `json:"metadata"`      // 额外信息
}

// TableName 指定表名
func (Character) TableName() string {
	return "characters"
}

// BeforeCreate GORM钩子 - 创建前
func (c *Character) BeforeCreate(tx *gorm.DB) error {
	if c.ID == "" {
		c.ID = uuid.New().String()
	}
	return nil
}

// AfterCreate GORM钩子 - 创建后
func (c *Character) AfterCreate(tx *gorm.DB) error {
	// 如果是玩家角色，更新用户统计
	if c.UserID != nil {
		var stats UserStats
		if err := tx.Where("user_id = ?", c.UserID).First(&stats).Error; err != nil {
			return err
		}
		return stats.IncrementWorldsJoined(tx)
	}
	return nil
}

// IsPlayer 检查是否为玩家角色
func (c *Character) IsPlayer() bool {
	return c.CharacterType == "player" && c.UserID != nil
}

// IsNPC 检查是否为NPC
func (c *Character) IsNPC() bool {
	return c.CharacterType == "npc" || c.UserID == nil
}

// IsCollective 检查是否为集体实体
func (c *Character) IsCollective() bool {
	return c.CharacterType == "collective"
}

// IsActive 检查角色是否活跃
func (c *Character) IsActive() bool {
	return c.Status == "active"
}

// IsDead 检查角色是否死亡
func (c *Character) IsDead() bool {
	return c.Status == "dead"
}

// MoveTo 移动到指定场景
func (c *Character) MoveTo(tx *gorm.DB, sceneID string) error {
	// 从当前场景移除
	if c.CurrentSceneID != nil {
		var currentScene Scene
		if err := tx.First(&currentScene, *c.CurrentSceneID).Error; err == nil {
			currentScene.RemoveEntity(tx, c.ID)
		}
	}

	// 移动到新场景
	c.CurrentSceneID = &sceneID
	if err := tx.Model(c).Update("current_scene_id", sceneID).Error; err != nil {
		return err
	}

	// 添加到新场景
	var newScene Scene
	if err := tx.First(&newScene, sceneID).Error; err != nil {
		return err
	}
	return newScene.AddEntity(tx, c.ID)
}

// AddTrait 添加特质
func (c *Character) AddTrait(tx *gorm.DB, trait string) error {
	// 检查是否已有该特质
	for _, t := range c.Traits {
		if t == trait {
			return nil
		}
	}

	c.Traits = append(c.Traits, trait)
	return tx.Model(c).Update("traits", c.Traits).Error
}

// RemoveTrait 移除特质
func (c *Character) RemoveTrait(tx *gorm.DB, trait string) error {
	filtered := make(StringArray, 0, len(c.Traits))
	for _, t := range c.Traits {
		if t != trait {
			filtered = append(filtered, t)
		}
	}

	c.Traits = filtered
	return tx.Model(c).Update("traits", c.Traits).Error
}

// HasTrait 检查是否有指定特质
func (c *Character) HasTrait(trait string) bool {
	for _, t := range c.Traits {
		if t == trait {
			return true
		}
	}
	return false
}

// AddMemory 添加记忆
func (c *Character) AddMemory(tx *gorm.DB, memory Memory) error {
	memory.ID = uuid.New()
	memory.CreatedAt = time.Now()
	memory.LastAccess = time.Now()
	memory.AccessCount = 0

	memories := c.GetMemories()
	memories = append(memories, memory)

	// 检查记忆数量限制
	maxMemories := 100 // 默认值，应该从世界配置获取
	if len(memories) > maxMemories {
		// 移除最不重要且最久未访问的记忆
		memories = c.pruneMemories(memories, maxMemories)
	}

	c.Memories = JSON{"memories": memories}
	return tx.Model(c).Update("memories", c.Memories).Error
}

// GetMemories 获取所有记忆
func (c *Character) GetMemories() []Memory {
	if memoriesData, ok := c.Memories["memories"].([]interface{}); ok {
		memories := make([]Memory, 0, len(memoriesData))
		for _, data := range memoriesData {
			if _, ok := data.(map[string]interface{}); ok {
				memory := Memory{}
				// 这里需要实现从map到Memory结构的转换
				// 为简化，暂时返回空切片
				memories = append(memories, memory)
			}
		}
		return memories
	}
	return []Memory{}
}

// pruneMemories 修剪记忆（移除不重要的记忆）
func (c *Character) pruneMemories(memories []Memory, maxCount int) []Memory {
	if len(memories) <= maxCount {
		return memories
	}

	// 简单的修剪策略：保留重要性高和最近访问的记忆
	// 这里可以实现更复杂的算法
	return memories[:maxCount]
}

// AddExperience 添加阅历
func (c *Character) AddExperience(tx *gorm.DB, expType, category string) error {
	experiences := c.GetExperiences()
	key := expType + "_" + category

	if exp, exists := experiences[key]; exists {
		if expData, ok := exp.(map[string]interface{}); ok {
			if count, ok := expData["count"].(float64); ok {
				expData["count"] = count + 1
				expData["last_updated"] = time.Now()
			}
		}
	} else {
		experiences[key] = Experience{
			Type:        expType,
			Category:    category,
			Level:       1,
			Count:       1,
			LastUpdated: time.Now(),
			Details:     []string{},
		}
	}

	c.Experiences = JSON(experiences)
	return tx.Model(c).Update("experiences", c.Experiences).Error
}

// GetExperiences 获取所有阅历
func (c *Character) GetExperiences() map[string]interface{} {
	if len(c.Experiences) == 0 {
		return make(map[string]interface{})
	}
	return map[string]interface{}(c.Experiences)
}

// GetExperienceLevel 获取指定类型的阅历等级
func (c *Character) GetExperienceLevel(expType, category string) int {
	experiences := c.GetExperiences()
	key := expType + "_" + category

	if exp, exists := experiences[key]; exists {
		if expData, ok := exp.(map[string]interface{}); ok {
			if level, ok := expData["level"].(float64); ok {
				return int(level)
			}
		}
	}
	return 0
}

// UpdateRelationship 更新关系
func (c *Character) UpdateRelationship(tx *gorm.DB, targetID uuid.UUID, relType string, strengthDelta int) error {
	relationships := c.GetRelationships()
	key := targetID.String()

	if rel, exists := relationships[key]; exists {
		if relData, ok := rel.(map[string]interface{}); ok {
			if strength, ok := relData["strength"].(float64); ok {
				newStrength := int(strength) + strengthDelta
				if newStrength > 100 {
					newStrength = 100
				} else if newStrength < -100 {
					newStrength = -100
				}
				relData["strength"] = newStrength
				relData["last_interact"] = time.Now()
			}
		}
	} else {
		relationships[key] = Relationship{
			TargetID:     targetID,
			Type:         relType,
			Strength:     strengthDelta,
			Trust:        50, // 默认信任度
			History:      []string{},
			LastInteract: time.Now(),
			Metadata:     make(map[string]interface{}),
		}
	}

	c.Relationships = JSON(relationships)
	return tx.Model(c).Update("relationships", c.Relationships).Error
}

// GetRelationships 获取所有关系
func (c *Character) GetRelationships() map[string]interface{} {
	if len(c.Relationships) == 0 {
		return make(map[string]interface{})
	}
	return map[string]interface{}(c.Relationships)
}

// GetRelationshipStrength 获取与指定角色的关系强度
func (c *Character) GetRelationshipStrength(targetID uuid.UUID) int {
	relationships := c.GetRelationships()
	key := targetID.String()

	if rel, exists := relationships[key]; exists {
		if relData, ok := rel.(map[string]interface{}); ok {
			if strength, ok := relData["strength"].(float64); ok {
				return int(strength)
			}
		}
	}
	return 0 // 中性关系
}

// UpdateLastAction 更新最后行动时间
func (c *Character) UpdateLastAction(tx *gorm.DB) error {
	now := time.Now()
	c.LastActionAt = &now
	return tx.Model(c).Update("last_action_at", now).Error
}
