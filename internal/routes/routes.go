package routes

import (
	"ai-text-game-iam-npc/internal/ai"
	"ai-text-game-iam-npc/internal/auth"
	"ai-text-game-iam-npc/internal/config"
	"ai-text-game-iam-npc/internal/game"
	"ai-text-game-iam-npc/internal/handlers"
	"ai-text-game-iam-npc/internal/validation"
	"ai-text-game-iam-npc/pkg/logger"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// SetupRoutes 设置路由
func SetupRoutes(
	cfg *config.Config,
	db *gorm.DB,
	logger logger.Logger,
) *gin.Engine {
	// 创建Gin引擎
	if cfg.Server.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	}
	r := gin.New()

	// 创建服务实例
	authService := auth.NewService(cfg, db)
	aiService := ai.NewService(cfg, db, logger)
	worldService := game.NewWorldService(db, logger)
	characterService := game.NewCharacterService(db, logger)
	sceneService := game.NewSceneService(db, logger)
	eventService := game.NewEventService(db, logger)
	stateService := game.NewStateService(db, logger)
	validationService := validation.NewValidationService(db, logger, nil)

	// 创建处理器实例
	authHandler := handlers.NewAuthHandler(authService)
	aiHandler := handlers.NewAIHandler(aiService)
	gameHandler := handlers.NewGameHandler(worldService, characterService, sceneService, eventService, stateService, aiService)
	validationHandler := handlers.NewValidationHandler(validationService)

	// 设置中间件
	r.Use(auth.LoggingMiddleware())
	r.Use(auth.CORSMiddleware())
	r.Use(auth.SecurityHeadersMiddleware())
	r.Use(auth.DevModeMiddleware()) // 添加开发模式中间件
	r.Use(gin.Recovery())

	// 健康检查处理函数
	healthHandler := func(c *gin.Context) {
		// 检查是否为开发模式
		isDev := cfg.Server.Environment == "development"
		response := gin.H{
			"status":      "ok",
			"service":     "ai-text-game-iam-npc",
			"version":     "1.0.0",
			"environment": cfg.Server.Environment,
		}

		// 数据库状态检查
		if sqlDB, err := db.DB(); err == nil {
			if err := sqlDB.Ping(); err == nil {
				response["database"] = "connected"
			} else {
				response["database"] = "error"
				response["database_error"] = err.Error()
			}
		} else {
			response["database"] = "error"
			response["database_error"] = err.Error()
		}

		// Redis状态检查（如果配置了Redis）
		if cfg.Redis.Host != "" && cfg.Redis.Port != 0 {
			// 尝试连接Redis进行健康检查
			response["redis"] = "configured"
			response["redis_note"] = "Redis已配置但未在当前实现中使用"
		} else {
			response["redis"] = "disabled"
			response["redis_note"] = "Redis未配置，运行在无缓存模式"
		}

		if isDev {
			response["auth_mode"] = "disabled"
			response["dev_features"] = []string{
				"认证跳过",
				"模拟用户登录",
				"放宽安全策略",
				"详细错误信息",
			}

			// 开发环境特殊说明
			if cfg.Redis.Host == "" {
				response["dev_mode"] = "sqlite_no_redis"
				response["dev_note"] = "开发环境使用SQLite数据库，无Redis缓存，功能完全正常"
			}
		}

		c.JSON(200, response)
	}

	// 注册健康检查路由 - 支持两种路径
	r.GET("/health", healthHandler)      // 原有路径
	r.GET("/api/health", healthHandler)  // 新增API路径，兼容前端调用

	// API路由组
	api := r.Group("/api/v1")

	// 认证相关路由（无需认证）
	authGroup := api.Group("/auth")
	{
		authGroup.GET("/:provider/url", authHandler.GetAuthURL)
		authGroup.GET("/:provider/callback", authHandler.HandleCallback)
		authGroup.POST("/refresh", authHandler.RefreshToken)
	}

	// 公开的校验配置路由（无需认证）
	api.GET("/validation/config", validationHandler.GetValidationConfig)

	// 需要认证的路由
	authenticated := api.Group("")
	authenticated.Use(auth.AuthMiddleware(authService))
	authenticated.Use(validation.ContentValidationMiddleware(validationService))
	{
		// 用户信息相关
		userGroup := authenticated.Group("/user")
		{
			userGroup.GET("/profile", authHandler.GetProfile)
			userGroup.PUT("/profile", authHandler.UpdateProfile)
			// TODO: 实现DeleteProfile方法
			// userGroup.DELETE("/profile", authHandler.DeleteProfile)
		}

		// 校验相关路由
		validationGroup := authenticated.Group("/validation")
		{
			validationGroup.POST("/validate", validationHandler.ValidateContent)
			validationGroup.POST("/batch-validate", validationHandler.BatchValidateContent)
			validationGroup.GET("/stats", validationHandler.GetValidationStats)
		}

		// AI相关路由
		aiGroup := authenticated.Group("/ai")
		{
			aiGroup.POST("/generate", aiHandler.GenerateContent)
			aiGroup.POST("/generate/scene", aiHandler.GenerateScene)
			aiGroup.POST("/generate/character", aiHandler.GenerateCharacter)
			aiGroup.POST("/generate/event", aiHandler.GenerateEvent)
			aiGroup.GET("/history", aiHandler.GetInteractionHistory)
			aiGroup.GET("/stats", aiHandler.GetTokenUsageStats)
		}

		// 游戏相关路由
		gameGroup := authenticated.Group("/game")
		{
			// 世界管理
			worldGroup := gameGroup.Group("/worlds")
			{
				worldGroup.POST("", gameHandler.CreateWorld)
				worldGroup.GET("/:world_id", gameHandler.GetWorld)
				worldGroup.PUT("/:world_id", gameHandler.UpdateWorld)
				worldGroup.DELETE("/:world_id", gameHandler.DeleteWorld)
				worldGroup.POST("/:world_id/join", gameHandler.JoinWorld)
				worldGroup.POST("/:world_id/leave", gameHandler.LeaveWorld)

				// 世界中的角色列表
				worldGroup.GET("/:world_id/characters", gameHandler.GetWorldCharacters)

				// 世界状态管理
				worldGroup.GET("/:world_id/state", gameHandler.GetWorldState)
				worldGroup.POST("/:world_id/time", gameHandler.UpdateWorldTime)
				worldGroup.POST("/:world_id/tick", gameHandler.ProcessWorldTick)
			}

			// 我的世界
			gameGroup.GET("/my-worlds", gameHandler.GetMyWorlds)

			// 公开世界
			gameGroup.GET("/public-worlds", gameHandler.GetPublicWorlds)

			// 角色管理
			characterGroup := gameGroup.Group("/characters")
			{
				characterGroup.POST("", gameHandler.CreateCharacter)
				characterGroup.GET("/:character_id", gameHandler.GetCharacter)
				characterGroup.PUT("/:character_id", gameHandler.UpdateCharacter)
				characterGroup.DELETE("/:character_id", gameHandler.DeleteCharacter)
				characterGroup.POST("/:character_id/move", gameHandler.MoveCharacter)
				characterGroup.POST("/:character_id/traits", gameHandler.AddCharacterTrait)
				characterGroup.POST("/:character_id/memories", gameHandler.AddCharacterMemory)
				characterGroup.POST("/:character_id/experiences", gameHandler.AddCharacterExperience)

				// 游戏交互API
				characterGroup.POST("/:character_id/actions", gameHandler.PerformAction)
				characterGroup.POST("/:character_id/interact/:target_character_id", gameHandler.InteractWithCharacter)
				characterGroup.POST("/:character_id/speak", gameHandler.SpeakInScene)
			}

			// 我的角色
			gameGroup.GET("/my-characters", gameHandler.GetMyCharacters)

			// 场景管理
			sceneGroup := gameGroup.Group("/scenes")
			{
				sceneGroup.POST("", gameHandler.CreateScene)
				sceneGroup.GET("/:scene_id", gameHandler.GetScene)
			}

			// 事件管理
			eventGroup := gameGroup.Group("/events")
			{
				eventGroup.POST("", gameHandler.CreateEvent)
				eventGroup.POST("/:event_id/process", gameHandler.ProcessEvent)
				eventGroup.POST("/trigger", gameHandler.TriggerEvent)
			}
		}
	}

	// 管理员路由（需要管理员权限）
	adminGroup := api.Group("/admin")
	adminGroup.Use(auth.AuthMiddleware(authService))
	// TODO: 实现RoleMiddleware
	// adminGroup.Use(auth.RoleMiddleware("admin"))
	{
		// 系统统计
		adminGroup.GET("/stats", func(c *gin.Context) {
			c.JSON(200, gin.H{
				"message": "管理员统计信息",
			})
		})

		// 用户管理
		adminGroup.GET("/users", func(c *gin.Context) {
			c.JSON(200, gin.H{
				"message": "用户列表",
			})
		})

		// 世界管理
		adminGroup.GET("/worlds", func(c *gin.Context) {
			c.JSON(200, gin.H{
				"message": "所有世界列表",
			})
		})
	}

	return r
}

// SetupTestRoutes 设置测试路由（用于单元测试）
func SetupTestRoutes(
	cfg *config.Config,
	db *gorm.DB,
	logger logger.Logger,
) *gin.Engine {
	gin.SetMode(gin.TestMode)

	// 创建服务实例
	authService := auth.NewService(cfg, db)
	aiService := ai.NewService(cfg, db, logger)
	worldService := game.NewWorldService(db, logger)
	characterService := game.NewCharacterService(db, logger)
	sceneService := game.NewSceneService(db, logger)
	eventService := game.NewEventService(db, logger)
	stateService := game.NewStateService(db, logger)

	// 创建处理器实例
	authHandler := handlers.NewAuthHandler(authService)
	aiHandler := handlers.NewAIHandler(aiService)
	gameHandler := handlers.NewGameHandler(worldService, characterService, sceneService, eventService, stateService, aiService)

	r := gin.New()

	// 基础中间件
	r.Use(gin.Recovery())

	// 测试路由
	api := r.Group("/api/v1")

	// 认证路由
	authGroup := api.Group("/auth")
	{
		authGroup.GET("/:provider/url", authHandler.GetAuthURL)
		authGroup.GET("/:provider/callback", authHandler.HandleCallback)
		authGroup.POST("/refresh", authHandler.RefreshToken)
	}

	// 需要认证的路由
	authenticated := api.Group("")
	authenticated.Use(auth.AuthMiddleware(authService))
	{
		// 用户路由
		userGroup := authenticated.Group("/user")
		{
			userGroup.GET("/profile", authHandler.GetProfile)
			userGroup.PUT("/profile", authHandler.UpdateProfile)
		}

		// AI路由
		aiGroup := authenticated.Group("/ai")
		{
			aiGroup.POST("/generate", aiHandler.GenerateContent)
			aiGroup.POST("/generate/scene", aiHandler.GenerateScene)
			aiGroup.POST("/generate/character", aiHandler.GenerateCharacter)
			aiGroup.POST("/generate/event", aiHandler.GenerateEvent)
			aiGroup.GET("/history", aiHandler.GetInteractionHistory)
			aiGroup.GET("/stats", aiHandler.GetTokenUsageStats)
		}

		// 游戏路由
		gameGroup := authenticated.Group("/game")
		{
			// 世界管理
			worldGroup := gameGroup.Group("/worlds")
			{
				worldGroup.POST("", gameHandler.CreateWorld)
				worldGroup.GET("/:world_id", gameHandler.GetWorld)
				worldGroup.PUT("/:world_id", gameHandler.UpdateWorld)
				worldGroup.DELETE("/:world_id", gameHandler.DeleteWorld)
				worldGroup.POST("/:world_id/join", gameHandler.JoinWorld)
				worldGroup.POST("/:world_id/leave", gameHandler.LeaveWorld)

				// 世界中的角色列表
				worldGroup.GET("/:world_id/characters", gameHandler.GetWorldCharacters)

				// 世界状态管理
				worldGroup.GET("/:world_id/state", gameHandler.GetWorldState)
				worldGroup.POST("/:world_id/time", gameHandler.UpdateWorldTime)
				worldGroup.POST("/:world_id/tick", gameHandler.ProcessWorldTick)
			}

			// 我的世界
			gameGroup.GET("/my-worlds", gameHandler.GetMyWorlds)

			// 公开世界
			gameGroup.GET("/public-worlds", gameHandler.GetPublicWorlds)

			// 角色管理
			characterGroup := gameGroup.Group("/characters")
			{
				characterGroup.POST("", gameHandler.CreateCharacter)
				characterGroup.GET("/:character_id", gameHandler.GetCharacter)
				characterGroup.PUT("/:character_id", gameHandler.UpdateCharacter)
				characterGroup.DELETE("/:character_id", gameHandler.DeleteCharacter)
				characterGroup.POST("/:character_id/move", gameHandler.MoveCharacter)
				characterGroup.POST("/:character_id/traits", gameHandler.AddCharacterTrait)
				characterGroup.POST("/:character_id/memories", gameHandler.AddCharacterMemory)
				characterGroup.POST("/:character_id/experiences", gameHandler.AddCharacterExperience)

				// 游戏交互API
				characterGroup.POST("/:character_id/actions", gameHandler.PerformAction)
				characterGroup.POST("/:character_id/interact/:target_character_id", gameHandler.InteractWithCharacter)
				characterGroup.POST("/:character_id/speak", gameHandler.SpeakInScene)
			}

			// 我的角色
			gameGroup.GET("/my-characters", gameHandler.GetMyCharacters)

			// 场景管理
			sceneGroup := gameGroup.Group("/scenes")
			{
				sceneGroup.POST("", gameHandler.CreateScene)
				sceneGroup.GET("/:scene_id", gameHandler.GetScene)
			}

			// 事件管理
			eventGroup := gameGroup.Group("/events")
			{
				eventGroup.POST("", gameHandler.CreateEvent)
				eventGroup.POST("/:event_id/process", gameHandler.ProcessEvent)
				eventGroup.POST("/trigger", gameHandler.TriggerEvent)
			}
		}
	}

	return r
}

// RegisterSwaggerRoutes 注册Swagger文档路由
func RegisterSwaggerRoutes(r *gin.Engine) {
	// 这里可以添加Swagger文档路由
	// 例如使用gin-swagger中间件
	/*
		import (
			swaggerFiles "github.com/swaggo/files"
			ginSwagger "github.com/swaggo/gin-swagger"
		)

		r.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
	*/
}

// SetupMiddleware 设置通用中间件
func SetupMiddleware(r *gin.Engine, logger logger.Logger) {
	// 日志中间件
	r.Use(auth.LoggingMiddleware())

	// CORS中间件
	r.Use(auth.CORSMiddleware())

	// 安全头中间件
	r.Use(auth.SecurityHeadersMiddleware())

	// 恢复中间件
	r.Use(gin.Recovery())

	// 限流中间件（可选）
	// r.Use(RateLimitMiddleware())

	// 请求大小限制中间件（可选）
	// r.Use(RequestSizeLimitMiddleware())
}

// SetupStaticRoutes 设置静态文件路由
func SetupStaticRoutes(r *gin.Engine) {
	// 静态文件服务
	r.Static("/static", "./web/static")
	r.StaticFile("/favicon.ico", "./web/static/favicon.ico")

	// 前端构建产物服务
	r.Static("/assets", "./web/static/dist/assets")
	r.StaticFile("/vite.svg", "./web/static/dist/vite.svg")

	// 前端单页应用路由
	// 对于非API路由，返回前端的index.html
	r.NoRoute(func(c *gin.Context) {
		path := c.Request.URL.Path

		// 如果是API路由，返回404
		if len(path) >= 4 && path[:4] == "/api" {
			c.JSON(404, gin.H{
				"error": "API endpoint not found",
				"path":  path,
			})
			return
		}

		// 如果是静态资源路由，返回404
		if len(path) >= 7 && path[:7] == "/static" {
			c.Status(404)
			return
		}

		// 其他路由返回前端应用
		c.File("./web/static/dist/index.html")
	})
}
