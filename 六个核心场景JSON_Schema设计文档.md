# 六个核心场景JSON Schema设计文档

## 概述

本文档描述了根据需求文档和数据设计文档重新设计的六个核心游戏场景的JSON Schema结构。这些Schema定义了AI文本生成接口的标准化输出格式，确保游戏世界的精确、稳定演化。

## 设计原则

### 1. 符合需求文档规范
- 严格按照需求文档中定义的六个核心场景进行设计
- 每个场景的输入输出规范完全符合文档要求
- 支持游戏时间参数的动态事件生成

### 2. 结构化和可扩展性
- 使用完整的JSON Schema定义，包含类型、描述、必需字段等
- 支持复杂的嵌套结构和数组类型
- 预留扩展字段，便于未来功能增强

### 3. 向后兼容性
- 保留原有的通用Schema定义
- 新增的核心场景Schema不影响现有功能
- 支持渐进式迁移

## 六个核心场景Schema

### 1. 世界创建场景 (world_creation)

**用途**: 根据玩家提供的世界名称和设定，生成多个详细的世界描述选项

**输入要求**:
- 世界名称（必需）
- 世界设定（可选）

**输出结构**:
```json
{
  "world_options": [
    {
      "world_name": "世界名称",
      "environment_description": "环境描述",
      "cultural_background": "文化背景",
      "historical_background": "历史背景",
      "geographical_info": {
        "major_regions": ["主要地理区域"],
        "important_landmarks": ["重要地标"],
        "climate_zones": ["气候区域"]
      },
      "world_rules": {
        "game_rules": ["游戏规则"],
        "restrictions": ["限制条件"],
        "magic_system": "魔法体系描述",
        "technology_level": "科技水平"
      },
      "theme": "世界主题",
      "difficulty_level": "难度等级",
      "estimated_scale": "世界规模"
    }
  ],
  "creation_timestamp": "创建时间戳"
}
```

**关键特性**:
- 提供3-5个不同的世界选项供玩家选择
- 包含完整的地理位置信息
- 支持可选的世界规则定义

### 2. 世界进入场景 (world_entry)

**用途**: 生成初始场景及其连接场景，为玩家提供游戏起点

**输入要求**:
- 世界名称
- 世界设定（可选）
- 世界描述
- 世界规则（可选）

**输出结构**:
```json
{
  "initial_scene": {
    "scene_id": "场景ID",
    "scene_name": "场景名称",
    "scene_description": "场景描述",
    "events": [事件列表],
    "items": [物品列表],
    "characters": [角色列表],
    "connections": [连接场景列表]
  },
  "connected_scenes": [连接场景数组],
  "game_time": 当前游戏时间,
  "world_status": {
    "current_season": "当前季节",
    "weather": "天气描述",
    "time_of_day": "时间段"
  },
  "player_spawn_info": {
    "spawn_scene_id": "出生场景ID",
    "spawn_description": "出生情况描述"
  }
}
```

**关键特性**:
- 支持游戏时间参数动态确定事件列表
- 包含完整的场景信息（事件、物品、角色）
- 提供初始场景周围的3-5个连接场景

### 3. 世界探索场景 (world_exploration)

**用途**: 基于当前位置和游戏时间，生成可探索的场景列表

**输入要求**:
- 世界基础信息（名称、设定、描述、规则）
- 当前场景周围信息

**输出结构**:
```json
{
  "explorable_scenes": [
    {
      "scene_id": "场景ID",
      "accessibility": {
        "is_accessible": true,
        "travel_time": 30,
        "travel_difficulty": "普通"
      },
      "dynamic_events": [基于游戏时间的动态事件],
      "available_items": [可获得物品],
      "present_characters": [当前角色]
    }
  ],
  "current_game_time": 当前游戏时间,
  "time_based_recommendations": [基于时间的推荐行动],
  "world_state_summary": {
    "active_global_events": ["全局活跃事件"],
    "weather_forecast": ["天气预报"],
    "seasonal_effects": ["季节影响"]
  }
}
```

**关键特性**:
- 支持基于游戏时间的动态事件生成
- 包含时间敏感的推荐行动
- 提供详细的可达性信息

### 4. 世界进程更新场景 (world_heartbeat)

**用途**: 定期更新世界状态，处理场景变化和全局事件

**输入要求**:
- 世界基础信息
- 当前游戏时间
- 现有场景状态（事件、物品、角色）

**输出结构**:
```json
{
  "scene_changes": [
    {
      "scene_id": "场景ID",
      "change_type": "变化类型",
      "updated_events": [更新后的事件列表],
      "updated_items": [更新后的物品列表],
      "updated_characters": [更新后的角色列表],
      "environmental_changes": {
        "weather_changes": ["天气变化"],
        "atmospheric_changes": ["氛围变化"]
      }
    }
  ],
  "global_changes": {
    "time_progression": 时间推进,
    "world_events": ["世界级事件"],
    "seasonal_transitions": ["季节转换"]
  },
  "update_summary": "更新总结",
  "next_heartbeat_time": 下次心跳时间,
  "affected_players": ["受影响的玩家ID"]
}
```

**关键特性**:
- 数组格式的场景变化列表
- 支持全局世界状态更新
- 包含详细的变化描述和影响评估

### 5. 书信对话场景 (letter_communication)

**用途**: 处理角色间的书信交流，生成个性化的回信内容

**输入要求**:
- 角色特性
- 相关记忆
- 书信历史

**输出结构**:
```json
{
  "letter_replies": [
    {
      "reply_id": "回复ID",
      "letter_content": "回信内容",
      "sender_character": {
        "character_id": "角色ID",
        "current_mood": "当前情绪",
        "relationship_context": {
          "relationship_type": "关系类型",
          "current_relationship_status": "当前关系状态"
        },
        "communication_style": {
          "formality_level": "正式程度",
          "writing_style": "写作风格"
        }
      },
      "content_analysis": {
        "main_topics": ["主要话题"],
        "emotional_tone": "情感基调",
        "requires_action": true
      },
      "memory_references": [相关记忆引用]
    }
  ],
  "communication_summary": {
    "total_participants": 参与者总数,
    "key_information_flow": ["关键信息流动"]
  },
  "follow_up_actions": [后续行动列表]
}
```

**关键特性**:
- 数组格式的对话回复列表
- 包含详细的角色信息和沟通风格
- 支持记忆系统集成

### 6. 事件交互场景 (event_interaction)

**用途**: 处理玩家与事件的交互，生成详细的交互结果

**输入要求**:
- 事件描述
- 物品描述
- 角色特性
- 角色阅历
- 角色行动

**输出结构**:
```json
{
  "interaction_results": [
    {
      "result_id": "结果ID",
      "event_result": {
        "outcome_type": "结果类型",
        "outcome_description": "结果描述",
        "success_degree": 0.8
      },
      "event_id": "事件ID",
      "participating_character": {
        "character_id": "角色ID",
        "role_in_event": "在事件中的角色",
        "performance_evaluation": {
          "skill_application": ["应用技能"],
          "experience_gained": ["获得经验"]
        }
      },
      "character_action_details": {
        "intended_action": "预期行动",
        "actual_execution": "实际执行",
        "resource_usage": [资源使用列表]
      },
      "consequences": {
        "immediate_effects": [即时影响],
        "long_term_consequences": ["长期后果"]
      },
      "experience_and_learning": {
        "experience_categories": [经验分类],
        "skill_developments": ["技能发展"]
      }
    }
  ],
  "event_summary": {
    "event_conclusion": "事件结论",
    "overall_success_rate": 0.75
  },
  "world_impact_assessment": {
    "local_impact": ["本地影响"],
    "historical_significance": "历史意义"
  },
  "follow_up_events": [后续事件列表]
}
```

**关键特性**:
- 数组格式的交互结果列表
- 包含详细的角色行动分析
- 支持经验和阅历系统集成
- 提供世界影响评估

## 技术实现

### Schema注册机制
```go
// 注册所有核心场景Schema
func (gsr *GameSchemaRegistry) registerAllSchemas() {
    // 六个核心场景
    gsr.schemas["world_creation"] = gsr.createWorldCreationSchema()
    gsr.schemas["world_entry"] = gsr.createWorldEntrySchema()
    gsr.schemas["world_exploration"] = gsr.createWorldExplorationSchema()
    gsr.schemas["world_heartbeat"] = gsr.createWorldHeartbeatSchema()
    gsr.schemas["letter_communication"] = gsr.createLetterCommunicationSchema()
    gsr.schemas["event_interaction"] = gsr.createEventInteractionSchema()
    
    // 保留原有通用Schema（向后兼容）
    // ...
}
```

### 使用方式
```go
// 获取特定场景的Schema
registry := ai.NewGameSchemaRegistry()
schema, err := registry.GetSchema("world_creation")

// 转换为Map格式用于API调用
schemaMap, err := registry.GetSchemaAsMap("world_creation")
```

## 验证结果

### 测试覆盖
- ✅ 所有六个核心场景Schema成功注册
- ✅ Schema结构完整，包含所有必需字段
- ✅ 支持复杂嵌套结构和数组类型
- ✅ JSON序列化和反序列化正常
- ✅ 与需求文档规范完全符合

### 性能指标
- 世界创建Schema: 6,663 字节
- 世界进入Schema: 21,752 字节
- 世界探索Schema: 16,387 字节
- 世界心跳Schema: 19,518 字节
- 书信对话Schema: 21,522 字节
- 事件交互Schema: 28,663 字节

## 后续计划

### 1. 集成测试
- 与AI接口的端到端测试
- 真实场景下的Schema验证
- 性能和稳定性测试

### 2. 文档完善
- API使用示例
- 最佳实践指南
- 故障排除文档

### 3. 功能扩展
- 支持更多游戏场景
- Schema版本管理
- 动态Schema更新

## 总结

本次重新设计的六个核心场景JSON Schema完全符合需求文档和数据设计文档的规范要求，提供了：

1. **完整的结构化定义**: 每个Schema都包含详细的字段类型、必需字段标识和字段描述
2. **灵活的数据格式**: 支持复杂的嵌套结构、数组类型和可选字段
3. **游戏逻辑支持**: 特别支持游戏时间参数、动态事件生成和状态管理
4. **向后兼容性**: 保留原有通用Schema，确保现有功能不受影响
5. **可扩展性**: 预留扩展字段，便于未来功能增强

这些Schema将作为AI文本生成接口的标准化输出格式，确保游戏世界的精确、稳定演化，为玩家提供丰富的游戏体验。
