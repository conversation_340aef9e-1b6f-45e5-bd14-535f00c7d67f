package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"ai-text-game-iam-npc/internal/config"
	"ai-text-game-iam-npc/internal/migration"
	"ai-text-game-iam-npc/internal/models"
	"ai-text-game-iam-npc/pkg/database"
	"ai-text-game-iam-npc/internal/routes"
	"ai-text-game-iam-npc/pkg/logger"

	"gorm.io/gorm"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 初始化日志
	logger := logger.New("info")
	logger.Info("正在启动AI文本游戏服务器", "version", "1.0.0")

	// 连接数据库
	db, err := database.New(&cfg.Database)
	if err != nil {
		logger.Fatal("连接数据库失败", "error", err)
	}
	logger.Info("数据库连接成功")

	// 执行数据库迁移
	logger.Info("开始执行数据库迁移")
	if err := performDatabaseMigration(db, &cfg.Database, logger); err != nil {
		logger.Fatal("数据库迁移失败", "error", err)
	}
	logger.Info("数据库迁移完成")

	// 设置路由
	router := routes.SetupRoutes(cfg, db, logger)

	// 设置静态文件服务
	routes.SetupStaticRoutes(router)

	// 创建HTTP服务器
	server := &http.Server{
		Addr:         fmt.Sprintf(":%s", cfg.Server.Port),
		Handler:      router,
		ReadTimeout:  time.Duration(cfg.Server.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(cfg.Server.WriteTimeout) * time.Second,
		IdleTimeout:  time.Duration(cfg.Server.IdleTimeout) * time.Second,
	}

	// 启动服务器
	go func() {
		logger.Info("服务器启动", "port", cfg.Server.Port, "environment", cfg.Server.Environment)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatal("服务器启动失败", "error", err)
		}
	}()

	// 等待中断信号以优雅地关闭服务器
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	logger.Info("正在关闭服务器...")

	// 设置关闭超时
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 优雅关闭服务器
	if err := server.Shutdown(ctx); err != nil {
		logger.Error("服务器强制关闭", "error", err)
	} else {
		logger.Info("服务器已优雅关闭")
	}
}

// performDatabaseMigration 执行数据库迁移
func performDatabaseMigration(db *gorm.DB, cfg *config.DatabaseConfig, logger logger.Logger) error {
	// 创建智能迁移器
	smartMigrator := migration.NewSmartMigrator(db, cfg, "migrations")

	// 检查数据库兼容性
	logger.Info("检查数据库兼容性")
	if err := smartMigrator.CheckCompatibility(); err != nil {
		logger.Warn("数据库兼容性检查失败，但继续执行", "error", err)
	}

	// 获取数据库信息
	info := smartMigrator.GetDatabaseInfo()
	logger.Info("数据库信息",
		"type", info["database_type"],
		"supports_jsonb", info["supports_jsonb"],
		"supports_uuid", info["supports_uuid"])

	// 定义所有需要迁移的模型
	models := []interface{}{
		&models.User{},
		&models.UserStats{},
		&models.World{},
		&models.Scene{},
		&models.Character{},
		&models.Entity{},
		&models.Event{},
		&models.AIInteraction{},
	}

	// 在开发环境下使用自动迁移
	if cfg.SSLMode == "development" || cfg.Host == "" {
		logger.Info("开发环境：使用GORM自动迁移")
		if err := smartMigrator.CreateDevelopmentSchema(models...); err != nil {
			return fmt.Errorf("开发环境数据库迁移失败: %w", err)
		}
		logger.Info("开发环境数据库迁移成功")
	} else {
		// 生产环境使用传统迁移文件
		logger.Info("生产环境：使用迁移文件")
		migrator, err := smartMigrator.CreateMigrator()
		if err != nil {
			return fmt.Errorf("创建迁移器失败: %w", err)
		}
		defer migrator.Close()

		// 执行向上迁移
		if err := migrator.Up(); err != nil {
			return fmt.Errorf("执行迁移失败: %w", err)
		}
		logger.Info("生产环境数据库迁移成功")
	}

	return nil
}
